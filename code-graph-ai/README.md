# Code Graph AI 模块

## 概述

Code Graph AI 模块是一个基于 Spring AI 1.0.0 的人工智能服务模块，提供向量生成、聊天对话和文档生成等功能。

## 主要特性

- **Spring Bean 支持**: 所有核心服务都声明为 Spring Bean，支持依赖注入
- **向后兼容**: 保持与现有代码的完全兼容性
- **工厂模式**: 提供统一的服务获取方式
- **资源管理**: 自动管理 Spring 应用上下文生命周期
- **现代化架构**: 使用 Java 17 和现代设计模式

## 核心组件

### 1. 向量生成器 (OpenAIVectorGenerator)

```java
// 方式1：使用工厂模式（推荐）
OpenAIVectorGenerator generator = AiServiceFactory.getVectorGenerator();
float[] vector = generator.generateVector("要生成向量的文本");

// 方式2：传统方式（仍然支持）
try (OpenAIVectorGenerator generator = new OpenAIVectorGenerator()) {
    float[] vector = generator.generateVector("要生成向量的文本");
}
```

### 2. 聊天服务 (SpringAiChatService)

```java
SpringAiChatService chatService = AiServiceFactory.getChatService();
String response = chatService.chat("你好，请介绍一下 Spring Framework");
```

### 3. 文档生成服务 (AIDocumentationService)

```java
AIDocumentationService docService = AiServiceFactory.getDocumentationService();
// 使用 DocumentationGenerationContext 生成文档
```

## 配置

### Spring AI 配置

配置类 `SpringAiConfig` 自动配置以下组件：
- OpenAI API 客户端
- 聊天模型 (ChatModel)
- 嵌入模型 (EmbeddingModel)
- 聊天客户端 (ChatClient)

### 环境变量

确保设置以下环境变量或在 AppConfig 中配置：
- `OPENAI_API_KEY`: OpenAI API 密钥
- `OPENAI_BASE_URL`: OpenAI API 基础 URL（可选）

## 使用指南

### 1. 在 Spring 应用中使用

```java
@Service
public class MyService {
    
    @Autowired
    private OpenAIVectorGenerator vectorGenerator;
    
    @Autowired
    private SpringAiChatService chatService;
    
    public void doSomething() {
        float[] vector = vectorGenerator.generateVector("文本");
        String response = chatService.chat("问题");
    }
}
```

### 2. 在非 Spring 应用中使用

```java
public class MyApplication {
    public static void main(String[] args) {
        // 预热服务
        AiServiceFactory.warmUp();
        
        try {
            // 使用服务
            OpenAIVectorGenerator generator = AiServiceFactory.getVectorGenerator();
            float[] vector = generator.generateVector("文本");
        } finally {
            // 关闭服务
            AiServiceFactory.shutdown();
        }
    }
}
```

### 3. 现有代码迁移

现有的代码无需修改，仍然可以正常工作：

```java
// 这种方式仍然有效
try (OpenAIVectorGenerator generator = new OpenAIVectorGenerator()) {
    float[] vector = generator.generateVector("文本");
}
```

## 架构设计

```
code-graph-ai/
├── config/
│   └── SpringAiConfig.java          # Spring AI 配置
├── context/
│   └── SpringContextFactory.java    # Spring 上下文工厂
├── factory/
│   └── AiServiceFactory.java        # AI 服务工厂
├── vector/
│   └── OpenAIVectorGenerator.java   # 向量生成器
├── chat/
│   └── SpringAiChatService.java     # 聊天服务
├── documentation/
│   ├── AIDocumentationService.java  # 文档生成服务
│   ├── AIPromptBuilder.java         # 提示词构建器
│   └── ...
└── example/
    └── AiServiceUsageExample.java   # 使用示例
```

## 依赖管理

模块使用以下主要依赖：
- Spring AI 1.0.0-M6
- Spring Framework 6.2.7
- Lombok 1.18.30

## 测试

运行集成测试：

```bash
./gradlew :code-graph-ai:test
```

注意：某些测试需要设置 `OPENAI_API_KEY` 环境变量。

## 最佳实践

1. **使用工厂模式**: 推荐使用 `AiServiceFactory` 获取服务实例
2. **资源管理**: 在应用启动时调用 `warmUp()`，关闭时调用 `shutdown()`
3. **异常处理**: 所有 AI 服务调用都应该包含适当的异常处理
4. **配置管理**: 确保 API 密钥等敏感信息安全存储

## 故障排除

### 常见问题

1. **依赖下载慢**: 确保网络连接正常，或使用代理
2. **API 调用失败**: 检查 API 密钥和网络连接
3. **Spring 上下文初始化失败**: 检查配置和依赖

### 日志配置

启用调试日志以获取更多信息：

```properties
logging.level.com.puti.code.ai=DEBUG
```

## 版本历史

- **v2.0.0**: 基于 Spring AI 1.0.0 的重构版本
- **v1.x.x**: 基于原生 HTTP 客户端的版本
