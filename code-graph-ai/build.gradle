description = 'code Graph AI - AI服务模块'

dependencies {
    // 依赖核心模块
    api project(':code-graph-base')

    // 暂时保留原有的 OpenAI 客户端，等待 Spring AI 稳定版本
    api "com.theokanning.openai-gpt3-java:service:${openaiVersion}"

    // Spring Framework (for dependency injection and configuration)
    implementation "org.springframework:spring-context:${springVersion}"
    implementation "org.springframework:spring-beans:${springVersion}"
    implementation "org.springframework:spring-core:${springVersion}"

    // HTTP client (still needed for some custom implementations)
    implementation "org.apache.httpcomponents:httpclient:${httpclientVersion}"
    implementation "org.apache.httpcomponents:httpcore:${httpcoreVersion}"

    // Lombok
    compileOnly "org.projectlombok:lombok:${lombokVersion}"
    annotationProcessor "org.projectlombok:lombok:${lombokVersion}"

    // Logging
    implementation "ch.qos.logback:logback-classic:${logbackVersion}"

    // Test dependencies
    testImplementation platform("org.junit:junit-bom:${junitVersion}")
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation "org.mockito:mockito-core:${mockitoVersion}"
    testImplementation "org.springframework:spring-test:${springVersion}"
}
