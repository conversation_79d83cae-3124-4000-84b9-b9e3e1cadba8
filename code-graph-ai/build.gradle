description = 'code Graph AI - AI服务模块'

dependencies {
    // 依赖核心模块
    api project(':code-graph-base')

    // Spring AI 1.0.0 dependencies
    api "org.springframework.ai:spring-ai-openai:${springAiVersion}"
    api "org.springframework.ai:spring-ai-core:${springAiVersion}"

    // Spring Framework (for dependency injection and configuration)
    implementation "org.springframework:spring-context:${springVersion}"
    implementation "org.springframework:spring-beans:${springVersion}"
    implementation "org.springframework:spring-core:${springVersion}"

    // HTTP client (still needed for some custom implementations)
    implementation "org.apache.httpcomponents:httpclient:${httpclientVersion}"
    implementation "org.apache.httpcomponents:httpcore:${httpcoreVersion}"

    // Lombok
    compileOnly "org.projectlombok:lombok:${lombokVersion}"
    annotationProcessor "org.projectlombok:lombok:${lombokVersion}"

    // Logging
    implementation "ch.qos.logback:logback-classic:${logbackVersion}"

    // Test dependencies
    testImplementation platform("org.junit:junit-bom:${junitVersion}")
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation "org.mockito:mockito-core:${mockitoVersion}"
    testImplementation "org.springframework:spring-test:${springVersion}"
}
