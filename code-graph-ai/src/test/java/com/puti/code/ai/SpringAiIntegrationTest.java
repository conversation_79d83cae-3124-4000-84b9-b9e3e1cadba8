package com.puti.code.ai;

import com.puti.code.ai.chat.SpringAiChatService;
import com.puti.code.ai.factory.AiServiceFactory;
import com.puti.code.ai.vector.OpenAIVectorGenerator;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Spring AI 集成测试
 * 验证重构后的 AI 服务是否正常工作
 * 
 * <AUTHOR>
 */
@Slf4j
public class SpringAiIntegrationTest {

    @BeforeAll
    static void setUp() {
        // 预热服务
        AiServiceFactory.warmUp();
    }

    @AfterAll
    static void tearDown() {
        // 关闭服务
        AiServiceFactory.shutdown();
    }

    @Test
    @EnabledIfEnvironmentVariable(named = "OPENAI_API_KEY", matches = ".*")
    public void testVectorGeneration() {
        OpenAIVectorGenerator vectorGenerator = AiServiceFactory.getVectorGenerator();
        try {
            String testText = "这是一个测试文本，用于验证向量生成功能";
            
            float[] vector = vectorGenerator.generateVector(testText);
            
            assertNotNull(vector, "生成的向量不应为空");
            assertTrue(vector.length > 0, "向量维度应大于0");
            
            log.info("向量生成测试通过，维度: {}", vector.length);
        } catch (Exception e) {
            log.error("向量生成测试失败", e);
            fail("向量生成测试失败: " + e.getMessage());
        }
    }

    @Test
    @EnabledIfEnvironmentVariable(named = "OPENAI_API_KEY", matches = ".*")
    public void testChatService() {
        SpringAiChatService chatService = AiServiceFactory.getChatService();
        try {
            String testMessage = "请简单介绍一下Java编程语言";
            
            String response = chatService.chat(testMessage);
            
            assertNotNull(response, "聊天响应不应为空");
            assertFalse(response.trim().isEmpty(), "聊天响应不应为空字符串");
            
            log.info("聊天服务测试通过，响应长度: {} 字符", response.length());
        } catch (Exception e) {
            log.error("聊天服务测试失败", e);
            fail("聊天服务测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testServiceAvailability() {
        try {
            SpringAiChatService chatService = AiServiceFactory.getChatService();
            // 这个测试不需要真实的 API 调用，只测试服务初始化
            assertNotNull(chatService, "聊天服务应该能够正常初始化");
            log.info("服务可用性测试通过");
        } catch (Exception e) {
            log.error("服务可用性测试失败", e);
            fail("服务初始化失败: " + e.getMessage());
        }
    }

    @Test
    public void testVectorGeneratorInitialization() {
        try {
            OpenAIVectorGenerator vectorGenerator = AiServiceFactory.getVectorGenerator();
            assertNotNull(vectorGenerator, "向量生成器应该能够正常初始化");
            log.info("向量生成器初始化测试通过");
        } catch (Exception e) {
            log.error("向量生成器初始化测试失败", e);
            fail("向量生成器初始化失败: " + e.getMessage());
        }
    }
}
