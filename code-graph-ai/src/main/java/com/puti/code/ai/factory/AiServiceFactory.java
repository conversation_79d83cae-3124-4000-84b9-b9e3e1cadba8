package com.puti.code.ai.factory;

import com.puti.code.ai.chat.SpringAiChatService;
import com.puti.code.ai.context.SpringContextFactory;
import com.puti.code.ai.documentation.AIDocumentationService;
import com.puti.code.ai.vector.OpenAIVectorGenerator;
import lombok.extern.slf4j.Slf4j;

/**
 * AI 服务工厂
 * 提供统一的 AI 服务实例获取方式
 * 
 * <AUTHOR>
 */
@Slf4j
public class AiServiceFactory {

    /**
     * 获取向量生成器实例
     */
    public static OpenAIVectorGenerator getVectorGenerator() {
        try {
            return SpringContextFactory.getBean(OpenAIVectorGenerator.class);
        } catch (Exception e) {
            log.warn("无法从 Spring 上下文获取向量生成器，使用默认构造函数", e);
            return new OpenAIVectorGenerator();
        }
    }

    /**
     * 获取聊天服务实例
     */
    public static SpringAiChatService getChatService() {
        try {
            return SpringContextFactory.getBean(SpringAiChatService.class);
        } catch (Exception e) {
            log.error("无法从 Spring 上下文获取聊天服务", e);
            throw new RuntimeException("聊天服务初始化失败", e);
        }
    }

    /**
     * 获取文档生成服务实例
     */
    public static AIDocumentationService getDocumentationService() {
        try {
            return SpringContextFactory.getBean(AIDocumentationService.class);
        } catch (Exception e) {
            log.error("无法从 Spring 上下文获取文档生成服务", e);
            throw new RuntimeException("文档生成服务初始化失败", e);
        }
    }

    /**
     * 预热所有服务
     * 在应用启动时调用，确保所有服务都已初始化
     */
    public static void warmUp() {
        log.info("开始预热 AI 服务...");
        
        try {
            getVectorGenerator();
            log.info("向量生成器预热完成");
        } catch (Exception e) {
            log.error("向量生成器预热失败", e);
        }

        try {
            getChatService();
            log.info("聊天服务预热完成");
        } catch (Exception e) {
            log.error("聊天服务预热失败", e);
        }

        try {
            getDocumentationService();
            log.info("文档生成服务预热完成");
        } catch (Exception e) {
            log.error("文档生成服务预热失败", e);
        }

        log.info("AI 服务预热完成");
    }

    /**
     * 关闭所有服务
     */
    public static void shutdown() {
        log.info("关闭 AI 服务...");
        SpringContextFactory.close();
        log.info("AI 服务已关闭");
    }
}
