package com.puti.code.ai.context;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

/**
 * Spring 应用上下文工厂
 * 用于在非 Spring 环境中获取 Spring Bean
 * 
 * <AUTHOR>
 */
@Slf4j
public class SpringContextFactory {

    private static volatile AnnotationConfigApplicationContext applicationContext;
    private static final Object lock = new Object();

    /**
     * 获取 Spring 应用上下文
     */
    public static AnnotationConfigApplicationContext getApplicationContext() {
        if (applicationContext == null) {
            synchronized (lock) {
                if (applicationContext == null) {
                    log.info("初始化 Spring 应用上下文");
                    applicationContext = new AnnotationConfigApplicationContext();
                    applicationContext.scan("com.puti.code.ai");
                    applicationContext.refresh();
                    
                    // 添加关闭钩子
                    Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                        if (applicationContext != null) {
                            log.info("关闭 Spring 应用上下文");
                            applicationContext.close();
                        }
                    }));
                }
            }
        }
        return applicationContext;
    }

    /**
     * 获取指定类型的 Bean
     */
    public static <T> T getBean(Class<T> beanClass) {
        return getApplicationContext().getBean(beanClass);
    }

    /**
     * 获取指定名称的 Bean
     */
    public static Object getBean(String beanName) {
        return getApplicationContext().getBean(beanName);
    }

    /**
     * 获取指定名称和类型的 Bean
     */
    public static <T> T getBean(String beanName, Class<T> beanClass) {
        return getApplicationContext().getBean(beanName, beanClass);
    }

    /**
     * 检查是否包含指定的 Bean
     */
    public static boolean containsBean(String beanName) {
        return getApplicationContext().containsBean(beanName);
    }

    /**
     * 手动关闭应用上下文
     */
    public static void close() {
        synchronized (lock) {
            if (applicationContext != null) {
                log.info("手动关闭 Spring 应用上下文");
                applicationContext.close();
                applicationContext = null;
            }
        }
    }

    /**
     * 重新初始化应用上下文
     */
    public static void refresh() {
        synchronized (lock) {
            if (applicationContext != null) {
                applicationContext.close();
            }
            applicationContext = new AnnotationConfigApplicationContext();
            applicationContext.scan("com.puti.code.ai");
            applicationContext.refresh();
            log.info("Spring 应用上下文已重新初始化");
        }
    }
}
