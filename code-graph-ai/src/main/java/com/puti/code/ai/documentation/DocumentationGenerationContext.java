package com.puti.code.ai.documentation;

import com.puti.code.base.entity.Documentation;
import com.puti.code.base.model.SubgraphData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文档生成上下文
 * 包含生成文档所需的所有上下文信息
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentationGenerationContext {
    
    /**
     * 入口点ID
     */
    private String entryPointId;
    
    /**
     * 当前生成层级
     */
    private int level;
    
    /**
     * 子图数据
     */
    private SubgraphData subgraph;
    
    /**
     * 前一层级的文档（用于增量生成）
     */
    private Documentation previousDocumentation;
    
    /**
     * 最大内容长度限制
     */
    private int maxLength;
    
    /**
     * 项目ID
     */
    private String projectId;
    
    /**
     * 分支名称
     */
    private String branchName;
    
    /**
     * 生成配置参数
     */
    private GenerationConfig config;
    
    /**
     * 生成配置类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GenerationConfig {
        
        /**
         * 是否包含代码示例
         */
        @Builder.Default
        private boolean includeCodeExamples = true;
        
        /**
         * 是否包含性能分析
         */
        @Builder.Default
        private boolean includePerformanceAnalysis = true;
        
        /**
         * 是否包含异常处理说明
         */
        @Builder.Default
        private boolean includeExceptionHandling = true;
        
        /**
         * 是否包含依赖关系分析
         */
        @Builder.Default
        private boolean includeDependencyAnalysis = true;
        
        /**
         * 文档风格（TECHNICAL, BUSINESS, MIXED）
         */
        @Builder.Default
        private DocumentationStyle style = DocumentationStyle.MIXED;
        
        /**
         * 详细程度（BRIEF, NORMAL, DETAILED）
         */
        @Builder.Default
        private DetailLevel detailLevel = DetailLevel.NORMAL;
        
        /**
         * 目标读者（DEVELOPER, ARCHITECT, BUSINESS）
         */
        @Builder.Default
        private TargetAudience targetAudience = TargetAudience.DEVELOPER;
    }
    
    /**
     * 文档风格枚举
     */
    public enum DocumentationStyle {
        TECHNICAL("技术导向"),
        BUSINESS("业务导向"), 
        MIXED("技术业务混合");
        
        private final String description;
        
        DocumentationStyle(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 详细程度枚举
     */
    public enum DetailLevel {
        BRIEF("简要"),
        NORMAL("正常"),
        DETAILED("详细");
        
        private final String description;
        
        DetailLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 目标读者枚举
     */
    public enum TargetAudience {
        DEVELOPER("开发人员"),
        ARCHITECT("架构师"),
        BUSINESS("业务人员");
        
        private final String description;
        
        TargetAudience(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 获取默认配置
     */
    public static GenerationConfig getDefaultConfig() {
        return GenerationConfig.builder().build();
    }
    
    /**
     * 获取针对特定层级的配置
     */
    public static GenerationConfig getConfigForLevel(int level) {
        switch (level) {
            case 1:
                return GenerationConfig.builder()
                        .includeCodeExamples(false)
                        .includePerformanceAnalysis(false)
                        .includeExceptionHandling(false)
                        .includeDependencyAnalysis(false)
                        .style(DocumentationStyle.BUSINESS)
                        .detailLevel(DetailLevel.BRIEF)
                        .targetAudience(TargetAudience.BUSINESS)
                        .build();

            case 2:
                return GenerationConfig.builder()
                        .includeCodeExamples(true)
                        .includePerformanceAnalysis(true)
                        .includeExceptionHandling(true)
                        .includeDependencyAnalysis(false)
                        .style(DocumentationStyle.MIXED)
                        .detailLevel(DetailLevel.NORMAL)
                        .targetAudience(TargetAudience.DEVELOPER)
                        .build();

            case 3:
                return GenerationConfig.builder()
                        .includeCodeExamples(true)
                        .includePerformanceAnalysis(true)
                        .includeExceptionHandling(true)
                        .includeDependencyAnalysis(true)
                        .style(DocumentationStyle.TECHNICAL)
                        .detailLevel(DetailLevel.DETAILED)
                        .targetAudience(TargetAudience.ARCHITECT)
                        .build();

            default:
                return getDefaultConfig();
        }
    }
    
    /**
     * 检查上下文是否有效
     */
    public boolean isValid() {
        return entryPointId != null && 
               !entryPointId.trim().isEmpty() &&
               level > 0 && 
               level <= 3 &&
               subgraph != null &&
               !subgraph.isEmpty();
    }
    
    /**
     * 获取上下文摘要信息
     */
    public String getSummary() {
        return String.format("Context[entryPoint=%s, level=%d, nodes=%d, edges=%d]",
                entryPointId, level, 
                subgraph != null ? subgraph.getTotalNodes() : 0,
                subgraph != null ? subgraph.getTotalEdges() : 0);
    }
}
