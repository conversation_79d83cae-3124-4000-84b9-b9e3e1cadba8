package com.puti.code.ai.example;

import com.puti.code.ai.factory.AiServiceFactory;
import com.puti.code.ai.vector.OpenAIVectorGenerator;
import com.puti.code.ai.chat.SpringAiChatService;
import com.puti.code.ai.documentation.AIDocumentationService;
import lombok.extern.slf4j.Slf4j;

/**
 * AI 服务使用示例
 * 展示如何在上游代码中使用重构后的 AI 服务
 * 
 * <AUTHOR>
 */
@Slf4j
public class AiServiceUsageExample {

    public static void main(String[] args) {
        // 预热服务
        AiServiceFactory.warmUp();

        try {
            // 示例1：使用向量生成器
            demonstrateVectorGeneration();

            // 示例2：使用聊天服务
            demonstrateChatService();

            // 示例3：使用文档生成服务
            demonstrateDocumentationService();

        } finally {
            // 关闭服务
            AiServiceFactory.shutdown();
        }
    }

    /**
     * 演示向量生成功能
     */
    private static void demonstrateVectorGeneration() {
        log.info("=== 向量生成示例 ===");
        
        try {
            OpenAIVectorGenerator vectorGenerator = AiServiceFactory.getVectorGenerator();
            
            String sampleText = "这是一个示例文本，用于演示向量生成功能";
            float[] vector = vectorGenerator.generateVector(sampleText);
            
            log.info("文本: {}", sampleText);
            log.info("生成的向量维度: {}", vector.length);
            log.info("向量前5个元素: [{}, {}, {}, {}, {}]", 
                    vector[0], vector[1], vector[2], vector[3], vector[4]);
                    
        } catch (Exception e) {
            log.error("向量生成示例失败", e);
        }
    }

    /**
     * 演示聊天服务功能
     */
    private static void demonstrateChatService() {
        log.info("=== 聊天服务示例 ===");
        
        try {
            SpringAiChatService chatService = AiServiceFactory.getChatService();
            
            String question = "请简单介绍一下 Spring Framework";
            String response = chatService.chat(question);
            
            log.info("问题: {}", question);
            log.info("AI 回答: {}", response);
            
        } catch (Exception e) {
            log.error("聊天服务示例失败", e);
        }
    }

    /**
     * 演示文档生成服务功能
     */
    private static void demonstrateDocumentationService() {
        log.info("=== 文档生成服务示例 ===");
        
        try {
            AIDocumentationService docService = AiServiceFactory.getDocumentationService();
            
            // 注意：这里只是演示如何获取服务实例
            // 实际的文档生成需要 DocumentationGenerationContext 参数
            log.info("文档生成服务已初始化: {}", docService.getClass().getSimpleName());
            
        } catch (Exception e) {
            log.error("文档生成服务示例失败", e);
        }
    }

    /**
     * 演示在现有代码中的集成方式
     */
    public static class LegacyCodeIntegration {
        
        /**
         * 替换原有的 OpenAIVectorGenerator 使用方式
         */
        public void oldWay() {
            // 旧的使用方式（仍然支持）
            try (OpenAIVectorGenerator generator = new OpenAIVectorGenerator()) {
                float[] vector = generator.generateVector("测试文本");
                log.info("旧方式生成向量，维度: {}", vector.length);
            } catch (Exception e) {
                log.error("旧方式失败", e);
            }
        }

        /**
         * 推荐的新使用方式
         */
        public void newWay() {
            // 新的使用方式（推荐）
            try {
                OpenAIVectorGenerator generator = AiServiceFactory.getVectorGenerator();
                float[] vector = generator.generateVector("测试文本");
                log.info("新方式生成向量，维度: {}", vector.length);
            } catch (Exception e) {
                log.error("新方式失败", e);
            }
        }
    }
}
