package com.puti.code.ai.config;

import com.puti.code.base.config.AppConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.openai.api.OpenAiApi.ChatModel;
import org.springframework.ai.openai.api.OpenAiApi.EmbeddingModel.ADA_002;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Spring AI 配置类
 * 配置 OpenAI 的聊天模型和嵌入模型
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class SpringAiConfig {

    private final AppConfig appConfig;

    public SpringAiConfig() {
        this.appConfig = AppConfig.getInstance();
        log.info("初始化 Spring AI 配置");
    }

    /**
     * 配置 OpenAI API 客户端
     */
    @Bean
    public OpenAiApi openAiApi() {
        String apiKey = appConfig.getChatApiKey();
        String baseUrl = appConfig.getChatUrl();
        
        if (apiKey == null || apiKey.trim().isEmpty()) {
            throw new IllegalStateException("OpenAI API Key 未配置");
        }
        
        log.info("配置 OpenAI API - Base URL: {}", baseUrl);
        
        return new OpenAiApi(baseUrl, apiKey);
    }

    /**
     * 配置聊天模型
     */
    @Bean
    public OpenAiChatModel chatModel(OpenAiApi openAiApi) {
        String modelName = appConfig.getChatModel();
        
        // 根据配置的模型名称选择对应的 ChatModel 枚举
        ChatModel chatModelEnum = getChatModelEnum(modelName);
        
        log.info("配置聊天模型: {}", chatModelEnum.getValue());
        
        return OpenAiChatModel.builder()
                .withOpenAiApi(openAiApi)
                .withOptions(org.springframework.ai.openai.OpenAiChatOptions.builder()
                        .withModel(chatModelEnum)
                        .withTemperature(0.7)
                        .withMaxTokens(4000)
                        .build())
                .build();
    }

    /**
     * 配置嵌入模型
     */
    @Bean
    public OpenAiEmbeddingModel embeddingModel(OpenAiApi openAiApi) {
        String embeddingModelName = appConfig.getEmbeddingModel();
        
        log.info("配置嵌入模型: {}", embeddingModelName);
        
        return OpenAiEmbeddingModel.builder()
                .withOpenAiApi(openAiApi)
                .withOptions(org.springframework.ai.openai.OpenAiEmbeddingOptions.builder()
                        .withModel(ADA_002)
                        .build())
                .build();
    }

    /**
     * 配置 ChatClient
     */
    @Bean
    public ChatClient chatClient(OpenAiChatModel chatModel) {
        return ChatClient.builder(chatModel).build();
    }

    /**
     * 根据模型名称获取对应的 ChatModel 枚举
     */
    private ChatModel getChatModelEnum(String modelName) {
        if (modelName == null || modelName.trim().isEmpty()) {
            return ChatModel.GPT_4_O_MINI;
        }
        
        switch (modelName.toLowerCase()) {
            case "gpt-4":
                return ChatModel.GPT_4;
            case "gpt-4-turbo":
                return ChatModel.GPT_4_TURBO;
            case "gpt-4o":
                return ChatModel.GPT_4_O;
            case "gpt-4o-mini":
                return ChatModel.GPT_4_O_MINI;
            case "gpt-3.5-turbo":
                return ChatModel.GPT_3_5_TURBO;
            case "deepseek":
                return ChatModel.GPT_4_O_MINI; // 使用兼容模型
            default:
                log.warn("未知的模型名称: {}，使用默认模型 GPT-4o-mini", modelName);
                return ChatModel.GPT_4_O_MINI;
        }
    }
}
