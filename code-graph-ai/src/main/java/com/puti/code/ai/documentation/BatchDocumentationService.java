package com.puti.code.ai.documentation;

import com.puti.code.base.model.MethodInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 分批文档生成服务
 * 处理大型第3层文档的分批生成和合并
 *
 * <AUTHOR>
 */
@Slf4j
public class BatchDocumentationService {

    private final AIPromptBuilder promptBuilder;
    private final AIDocumentationService aiDocumentationService;

    // 用于并行处理的线程池
    private final ExecutorService executorService = Executors.newFixedThreadPool(3);

    /**
     * 构造函数
     */
    public BatchDocumentationService(AIPromptBuilder promptBuilder, AIDocumentationService aiDocumentationService) {
        this.promptBuilder = promptBuilder;
        this.aiDocumentationService = aiDocumentationService;
    }

    /**
     * 检查是否需要分批处理
     *
     * @param context 文档生成上下文
     * @return 是否需要分批处理
     */
    public boolean needsBatchProcessing(DocumentationGenerationContext context) {
        // 获取当前层级的方法
        List<MethodInfo> methods;
        switch (context.getLevel()) {
            case 1:
                methods = context.getSubgraph().getMethodsAtLevel(1);
                break;
            case 2:
                methods = context.getSubgraph().getMethodsAtLevel(2);
                break;
            case 3:
                methods = context.getSubgraph().getAllMethods();
                break;
            default:
                methods = context.getSubgraph().getAllMethods();
                break;
        }

        // 获取模型配置
        ModelConfig modelConfig = promptBuilder.getModelConfig();
        int maxSize = BatchProcessingConfig.getMaxCharsForLevel(context.getLevel(),
                modelConfig.getMaxTokens(), modelConfig.getCharsPerToken(), modelConfig.getReservedTokens());

        int estimatedSize = estimateContentSize(methods);

        boolean needsBatch = estimatedSize > maxSize;
        if (needsBatch) {
            log.info("第{}层内容过大({} 字符 > {} 字符)，需要分批处理",
                    context.getLevel(), estimatedSize, maxSize);
        }

        return needsBatch;
    }

    /**
     * 分批生成文档（支持所有层级）
     *
     * @param context 文档生成上下文
     * @return 合并后的完整文档内容
     */
    public String generateBatchDocumentation(DocumentationGenerationContext context) {
        try {
            log.info("开始分批生成第{}层文档，入口点: {}", context.getLevel(), context.getEntryPointId());

            // 1. 构建分批提示词
            List<String> batchPrompts = buildBatchPrompts(context);

            if (batchPrompts.isEmpty()) {
                log.error("构建分批提示词失败");
                return null;
            }

            log.info("将第{}层文档分为 {} 批处理", context.getLevel(), batchPrompts.size());

            // 2. 并行生成各批次内容
            List<CompletableFuture<String>> futures = new ArrayList<>();

            for (int i = 0; i < batchPrompts.size(); i++) {
                final int batchIndex = i;
                final String prompt = batchPrompts.get(i);

                CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        log.info("开始生成第 {} 批文档内容", batchIndex + 1);
                        String content = callAIServiceWithRetry(prompt);
                        log.info("完成第 {} 批文档生成，内容长度: {} 字符", batchIndex + 1,
                                content != null ? content.length() : 0);
                        return content;
                    } catch (Exception e) {
                        log.error("生成第 {} 批文档失败", batchIndex + 1, e);
                        return null;
                    }
                }, executorService);

                futures.add(future);
            }

            // 3. 等待所有批次完成并收集结果
            List<String> batchResults = futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());

            // 4. 检查是否有失败的批次
            long failedBatches = batchResults.stream().filter(result -> result == null || result.trim().isEmpty()).count();
            if (failedBatches > 0) {
                log.warn("有 {} 个批次生成失败", failedBatches);
            }

            // 5. 合并所有批次的内容
            String mergedContent = mergeBatchResults(batchResults, context);

            log.info("完成第{}层分批文档生成，总长度: {} 字符", context.getLevel(), mergedContent.length());

            return mergedContent;

        } catch (Exception e) {
            log.error("分批生成第{}层文档时发生错误", context.getLevel(), e);
            return null;
        }
    }

    /**
     * 构建分批提示词（支持所有层级）
     */
    private List<String> buildBatchPrompts(DocumentationGenerationContext context) {
        return switch (context.getLevel()) {
            case 1 -> promptBuilder.buildLevel1BatchPrompts(context);
            case 2 -> promptBuilder.buildLevel2BatchPrompts(context);
            case 3 -> promptBuilder.buildLevel3BatchPrompts(context);
            default -> promptBuilder.buildLevel3BatchPrompts(context);
        };
    }

    /**
     * 合并批次结果
     */
    private String mergeBatchResults(List<String> batchResults, DocumentationGenerationContext context) {
        StringBuilder merged = new StringBuilder();

        // 添加文档标题和概述
        merged.append("# ").append(context.getEntryPointId()).append(" 技术说明书\n\n");
        merged.append("> 本文档通过分批分析生成，包含完整的代码分析和技术细节\n\n");

        // 添加目录
        merged.append("## 目录\n\n");
        for (int i = 0; i < batchResults.size(); i++) {
            if (batchResults.get(i) != null && !batchResults.get(i).trim().isEmpty()) {
                if (i == 0) {
                    merged.append("1. [整体架构分析](#整体架构分析)\n");
                } else if (i == batchResults.size() - 1) {
                    merged.append(String.format("%d. [技术总结](#技术总结)\n", i + 1));
                } else {
                    merged.append(String.format("%d. [详细分析第%d部分](#详细分析第%d部分)\n", i + 1, i, i));
                }
            }
        }
        merged.append("\n---\n\n");

        // 合并各批次内容
        for (int i = 0; i < batchResults.size(); i++) {
            String batchContent = batchResults.get(i);
            if (batchContent != null && !batchContent.trim().isEmpty()) {
                if (i == 0) {
                    merged.append("## 整体架构分析\n\n");
                } else if (i == batchResults.size() - 1) {
                    merged.append("## 技术总结\n\n");
                } else {
                    merged.append("## 详细分析第").append(i).append("部分\n\n");
                }

                merged.append(batchContent.trim()).append("\n\n");
                merged.append("---\n\n");
            }
        }

        // 添加生成信息
        merged.append("## 生成信息\n\n");
        merged.append("- **生成方式**: 分批处理\n");
        merged.append("- **批次数量**: ").append(batchResults.size()).append("\n");
        merged.append("- **生成时间**: ").append(java.time.LocalDateTime.now().toString()).append("\n");
        merged.append("- **入口点**: ").append(context.getEntryPointId()).append("\n");

        return merged.toString();
    }

    /**
     * 带重试机制的AI服务调用
     */
    private String callAIServiceWithRetry(String prompt) {
        int maxRetries = BatchProcessingConfig.getMaxRetryAttempts();
        long retryDelay = BatchProcessingConfig.getRetryDelayMs();

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return aiDocumentationService.callAIService(prompt);
            } catch (Exception e) {
                log.warn("AI服务调用失败，第 {} 次尝试: {}", attempt, e.getMessage());

                if (attempt < maxRetries) {
                    try {
                        Thread.sleep(retryDelay * attempt);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        log.error("AI服务调用失败，已重试 {} 次", maxRetries);
        return null;
    }

    /**
     * 估算内容大小
     */
    private int estimateContentSize(List<MethodInfo> methods) {
        int totalSize = 0;
        for (MethodInfo method : methods) {
            totalSize += method.getFullName() != null ? method.getFullName().length() : 0;
            totalSize += method.getSignature() != null ? method.getSignature().length() : 0;
            totalSize += method.getDescription() != null ? method.getDescription().length() : 0;

            if (method.getContent() != null && !method.getContent().trim().isEmpty()) {
                // 简单估算：如果是压缩内容，假设解压后是原来的3倍
                totalSize += method.getContent().length() * 3;
            }

            totalSize += 200; // 预留格式化字符
        }
        return totalSize;
    }

    /**
     * 关闭线程池
     */
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
