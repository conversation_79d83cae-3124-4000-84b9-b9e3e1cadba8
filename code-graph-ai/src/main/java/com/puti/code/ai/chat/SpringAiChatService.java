package com.puti.code.ai.chat;

import com.google.gson.JsonObject;
import com.google.gson.JsonArray;
import com.puti.code.base.config.AppConfig;
import com.puti.code.base.util.Json;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * AI 聊天服务
 * 重构版本，改进了代码结构和错误处理
 * 声明为 Spring Bean
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SpringAiChatService implements AutoCloseable {

    private final AppConfig config;
    private final CloseableHttpClient httpClient;

    public SpringAiChatService() {
        this.config = AppConfig.getInstance();
        this.httpClient = HttpClients.createDefault();
        log.info("AI 聊天服务初始化完成");
    }

    /**
     * 发送聊天消息并获取响应
     *
     * @param message 用户消息
     * @return AI 响应内容
     */
    public String chat(String message) {
        if (message == null || message.trim().isEmpty()) {
            log.warn("提供的消息为空");
            return "";
        }

        try {
            log.debug("发送聊天消息，长度: {} 字符", message.length());

            HttpPost httpPost = createChatRequest(message);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                return processChatResponse(response);
            }

        } catch (Exception e) {
            log.error("聊天请求失败", e);
            return "";
        }
    }

    /**
     * 创建聊天请求
     */
    private HttpPost createChatRequest(String message) {
        HttpPost httpPost = new HttpPost(config.getChatUrl());

        JsonObject requestObject = new JsonObject();
        requestObject.addProperty("model", config.getChatModel());
        requestObject.addProperty("max_tokens", 4000);
        requestObject.addProperty("temperature", 0.7);

        // 构建消息数组
        JsonArray messagesArray = new JsonArray();
        JsonObject messageObject = new JsonObject();
        messageObject.addProperty("role", "user");
        messageObject.addProperty("content", message);
        messagesArray.add(messageObject);

        requestObject.add("messages", messagesArray);

        String requestBody = Json.toJson(requestObject);

        StringEntity requestEntity = new StringEntity(requestBody, ContentType.APPLICATION_JSON);
        httpPost.setEntity(requestEntity);

        // 设置请求头
        httpPost.setHeader("Authorization", "Bearer " + config.getChatApiKey());
        httpPost.setHeader("Content-Type", "application/json");

        return httpPost;
    }

    /**
     * 处理聊天响应
     */
    private String processChatResponse(CloseableHttpResponse response) throws IOException {
        int statusCode = response.getStatusLine().getStatusCode();
        HttpEntity responseEntity = response.getEntity();
        String responseBody = responseEntity != null ? EntityUtils.toString(responseEntity) : null;

        log.debug("AI服务响应状态码: {}", statusCode);

        if (statusCode == 200) {
            return parseChatResponse(responseBody);
        } else {
            log.error("AI服务调用失败，状态码: {}, 响应: {}", statusCode, responseBody);
            return "";
        }
    }

    /**
     * 解析聊天响应
     */
    private String parseChatResponse(String responseBody) throws IOException {
        try {
            JsonObject jsonObject = Json.fromJson(responseBody, JsonObject.class);

            // 解析OpenAI格式的响应
            if (jsonObject != null && jsonObject.has("choices")) {
                JsonArray choicesArray = jsonObject.getAsJsonArray("choices");
                if (choicesArray.size() > 0) {
                    JsonObject firstChoice = choicesArray.get(0).getAsJsonObject();
                    if (firstChoice.has("message")) {
                        JsonObject messageObject = firstChoice.getAsJsonObject("message");
                        if (messageObject.has("content")) {
                            String content = messageObject.get("content").getAsString();
                            log.debug("收到 AI 响应，长度: {} 字符", content.length());
                            return content;
                        }
                    }
                }
            }

            log.error("无法从AI响应中解析内容: {}", responseBody);
            return "";

        } catch (Exception e) {
            log.error("解析AI响应时发生错误: {}", responseBody, e);
            throw new IOException("解析AI响应失败", e);
        }
    }

    /**
     * 检查服务是否可用
     *
     * @return 服务状态
     */
    public boolean isAvailable() {
        try {
            String testResponse = chat("Hello");
            return testResponse != null && !testResponse.trim().isEmpty();
        } catch (Exception e) {
            log.error("服务可用性检查失败", e);
            return false;
        }
    }

    /**
     * 关闭资源
     */
    @Override
    public void close() {
        try {
            if (httpClient != null) {
                httpClient.close();
            }
            log.info("AI 聊天服务资源已关闭");
        } catch (IOException e) {
            log.error("关闭HTTP客户端失败", e);
        }
    }
}
