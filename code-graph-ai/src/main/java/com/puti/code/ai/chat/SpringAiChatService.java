package com.puti.code.ai.chat;

import com.puti.code.ai.config.SpringAiConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

/**
 * Spring AI 聊天服务
 * 使用 Spring AI 1.0.0 提供聊天功能
 * 
 * <AUTHOR>
 */
@Slf4j
public class SpringAiChatService implements AutoCloseable {

    private final ChatClient chatClient;
    private final OpenAiChatModel chatModel;
    private final AnnotationConfigApplicationContext applicationContext;

    public SpringAiChatService() {
        // 初始化 Spring 应用上下文
        this.applicationContext = new AnnotationConfigApplicationContext(SpringAiConfig.class);
        this.chatModel = applicationContext.getBean(OpenAiChatModel.class);
        this.chatClient = applicationContext.getBean(ChatClient.class);
        
        log.info("Spring AI 聊天服务初始化完成");
    }

    /**
     * 发送聊天消息并获取响应
     * 
     * @param message 用户消息
     * @return AI 响应内容
     */
    public String chat(String message) {
        if (message == null || message.trim().isEmpty()) {
            log.warn("提供的消息为空");
            return "";
        }

        try {
            log.debug("发送聊天消息，长度: {} 字符", message.length());
            
            String response = chatClient.prompt()
                    .user(message)
                    .call()
                    .content();
            
            log.debug("收到 AI 响应，长度: {} 字符", response != null ? response.length() : 0);
            return response != null ? response : "";
            
        } catch (Exception e) {
            log.error("聊天请求失败", e);
            return "";
        }
    }

    /**
     * 发送聊天消息并获取完整响应对象
     * 
     * @param message 用户消息
     * @return ChatResponse 对象
     */
    public ChatResponse chatWithResponse(String message) {
        if (message == null || message.trim().isEmpty()) {
            log.warn("提供的消息为空");
            return null;
        }

        try {
            log.debug("发送聊天消息（完整响应），长度: {} 字符", message.length());
            
            ChatResponse response = chatClient.prompt()
                    .user(message)
                    .call()
                    .chatResponse();
            
            log.debug("收到完整 AI 响应");
            return response;
            
        } catch (Exception e) {
            log.error("聊天请求失败", e);
            return null;
        }
    }

    /**
     * 使用自定义 Prompt 发送请求
     * 
     * @param prompt 自定义 Prompt 对象
     * @return AI 响应内容
     */
    public String chatWithPrompt(Prompt prompt) {
        if (prompt == null) {
            log.warn("提供的 Prompt 为空");
            return "";
        }

        try {
            log.debug("发送自定义 Prompt");
            
            ChatResponse response = chatModel.call(prompt);
            
            if (response.getResults().isEmpty()) {
                log.warn("AI 响应结果为空");
                return "";
            }
            
            String content = response.getResults().get(0).getOutput().getContent();
            log.debug("收到 AI 响应，长度: {} 字符", content != null ? content.length() : 0);
            
            return content != null ? content : "";
            
        } catch (Exception e) {
            log.error("自定义 Prompt 请求失败", e);
            return "";
        }
    }

    /**
     * 检查服务是否可用
     * 
     * @return 服务状态
     */
    public boolean isAvailable() {
        try {
            String testResponse = chat("Hello");
            return testResponse != null && !testResponse.trim().isEmpty();
        } catch (Exception e) {
            log.error("服务可用性检查失败", e);
            return false;
        }
    }

    /**
     * 关闭资源
     */
    @Override
    public void close() {
        try {
            if (applicationContext != null) {
                applicationContext.close();
                log.info("Spring AI 聊天服务已关闭");
            }
        } catch (Exception e) {
            log.error("关闭 Spring AI 聊天服务失败", e);
        }
    }
}
