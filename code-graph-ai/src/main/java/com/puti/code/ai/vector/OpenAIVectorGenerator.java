package com.puti.code.ai.vector;

import com.google.gson.JsonObject;
import com.google.gson.JsonArray;
import com.puti.code.base.config.AppConfig;
import com.puti.code.base.util.Json;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * OpenAI向量生成器
 * 重构版本，改进了代码结构和错误处理
 * 声明为 Spring Bean，支持依赖注入
 */
@Slf4j
@Component
public class OpenAIVectorGenerator implements AutoCloseable {

    private final AppConfig config;
    private final CloseableHttpClient httpClient;

    /**
     * 默认构造函数
     */
    public OpenAIVectorGenerator() {
        this.config = AppConfig.getInstance();
        this.httpClient = HttpClients.createDefault();
        log.info("OpenAI向量生成器初始化完成，使用改进的架构");
    }

    /**
     * 生成文本向量
     *
     * @param text 文本
     * @return 向量
     */
    public float[] generateVector(String text) {
        if (text == null || text.trim().isEmpty()) {
            log.warn("提供的文本为空，返回零向量");
            return new float[config.getMilvusDimension()];
        }

        try {
            // 创建 HTTP 请求
            HttpPost httpPost = createHttpPost(text);

            // 执行请求并获取响应
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                return processResponse(response);
            }
        } catch (Exception e) {
            log.error("生成向量失败", e);
            return new float[config.getMilvusDimension()];
        }
    }

    /**
     * 创建 HTTP 请求
     */
    private HttpPost createHttpPost(String text) {
        HttpPost httpPost = new HttpPost(config.getEmbeddingUrl());

        // 预处理文本：移除换行符，替换为空格，并转义特殊字符
        String processedText = preprocessText(text);
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("input", processedText);
        jsonObject.addProperty("model", config.getEmbeddingModel());

        StringEntity requestEntity = new StringEntity(Json.toJson(jsonObject), ContentType.APPLICATION_JSON);
        httpPost.setEntity(requestEntity);

        // 设置请求头
        httpPost.setHeader("Authorization", "Bearer " + config.getEmbeddingApiKey());
        httpPost.setHeader("Content-Type", "application/json");

        return httpPost;
    }

    /**
     * 处理 HTTP 响应
     */
    private float[] processResponse(CloseableHttpResponse response) throws IOException {
        int statusCode = response.getStatusLine().getStatusCode();
        HttpEntity responseEntity = response.getEntity();
        String responseBody = responseEntity != null ? EntityUtils.toString(responseEntity) : null;

        log.debug("AI服务响应状态码: {}", statusCode);

        if (statusCode == 200) {
            return parseEmbeddingResponse(responseBody);
        } else {
            log.error("AI服务调用失败，状态码: {}, 响应: {}", statusCode, responseBody);
            return new float[config.getMilvusDimension()];
        }
    }

    /**
     * 解析嵌入响应
     */
    private float[] parseEmbeddingResponse(String responseBody) throws IOException {
        try {
            JsonObject jsonObject = Json.fromJson(responseBody, JsonObject.class);

            if (jsonObject == null || !jsonObject.has("data")) {
                log.error("响应格式错误，缺少 data 字段: {}", responseBody);
                return new float[config.getMilvusDimension()];
            }

            JsonArray dataArray = jsonObject.getAsJsonArray("data");
            if (dataArray.size() == 0) {
                log.error("响应中 data 数组为空: {}", responseBody);
                return new float[config.getMilvusDimension()];
            }

            JsonObject firstData = dataArray.get(0).getAsJsonObject();
            if (!firstData.has("embedding")) {
                log.error("响应中未找到嵌入向量: {}", responseBody);
                return new float[config.getMilvusDimension()];
            }

            JsonArray embeddingArray = firstData.getAsJsonArray("embedding");
            float[] result = new float[embeddingArray.size()];

            for (int i = 0; i < embeddingArray.size(); i++) {
                result[i] = embeddingArray.get(i).getAsFloat();
            }

            log.debug("成功生成向量，维度: {}", result.length);
            return result;

        } catch (Exception e) {
            log.error("解析嵌入响应时发生错误: {}", responseBody, e);
            throw new IOException("解析嵌入响应失败", e);
        }
    }

    /**
     * 预处理文本：移除换行符，替换为空格，并转义特殊字符
     */
    private String preprocessText(String text) {
        if (text == null) {
            return "";
        }

        return text.replace("\n", " ")
                .replace("\r", " ")
                .replace("\t", " ")
                .trim();
    }

    /**
     * 关闭资源
     */
    @Override
    public void close() {
        try {
            if (httpClient != null) {
                httpClient.close();
            }
            log.info("OpenAI向量生成器资源已关闭");
        } catch (IOException e) {
            log.error("关闭HTTP客户端失败", e);
        }
    }
}
