package com.puti.code.ai.vector;

import com.puti.code.ai.config.SpringAiConfig;
import com.puti.code.base.config.AppConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.embedding.EmbeddingResponse;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import java.util.List;

/**
 * OpenAI向量生成器
 * 使用 Spring AI 1.0.0 重构
 */
@Slf4j
public class OpenAIVectorGenerator implements AutoCloseable {

    private final AppConfig config;
    private final EmbeddingModel embeddingModel;
    private final AnnotationConfigApplicationContext applicationContext;

    public OpenAIVectorGenerator() {
        this.config = AppConfig.getInstance();

        // 初始化 Spring 应用上下文
        this.applicationContext = new AnnotationConfigApplicationContext(SpringAiConfig.class);
        this.embeddingModel = applicationContext.getBean(OpenAiEmbeddingModel.class);

        log.info("OpenAI向量生成器初始化完成，使用 Spring AI 1.0.0");
    }

    /**
     * 生成文本向量
     *
     * @param text 文本
     * @return 向量
     */
    public float[] generateVector(String text) {
        if (text == null || text.trim().isEmpty()) {
            log.warn("提供的文本为空，返回零向量");
            return new float[config.getMilvusDimension()];
        }

        try {
            // 预处理文本：移除换行符，替换为空格
            String processedText = preprocessText(text);

            // 创建文档对象
            Document document = new Document(processedText);

            // 使用 Spring AI 生成嵌入向量
            EmbeddingResponse response = embeddingModel.embedForResponse(List.of(document));

            if (response.getResults().isEmpty()) {
                log.error("嵌入响应为空");
                return new float[config.getMilvusDimension()];
            }

            // 获取第一个结果的向量
            List<Double> embedding = response.getResults().get(0).getOutput();

            // 转换为 float 数组
            float[] result = new float[embedding.size()];
            for (int i = 0; i < embedding.size(); i++) {
                result[i] = embedding.get(i).floatValue();
            }

            log.debug("成功生成向量，维度: {}", result.length);
            return result;

        } catch (Exception e) {
            log.error("生成向量失败", e);
            return new float[config.getMilvusDimension()];
        }
    }

    /**
     * 预处理文本：移除换行符，替换为空格，并转义特殊字符
     */
    private String preprocessText(String text) {
        if (text == null) {
            return "";
        }

        return text.replace("\n", " ")
                .replace("\r", " ")
                .replace("\t", " ")
                .trim();
    }

    /**
     * 关闭资源
     */
    @Override
    public void close() {
        try {
            if (applicationContext != null) {
                applicationContext.close();
                log.info("Spring 应用上下文已关闭");
            }
        } catch (Exception e) {
            log.error("关闭 Spring 应用上下文失败", e);
        }
    }
}
