# Code Graph AI 模块迁移指南

## 概述

本指南帮助您从旧版本的 Code Graph AI 模块迁移到基于 Spring AI 1.0.0 的新版本。

## 主要变更

### 1. 架构变更

- **从单例模式改为 Spring Bean**: 所有核心服务现在都是 Spring Bean
- **引入依赖注入**: 使用 Spring 的依赖注入机制
- **工厂模式**: 提供统一的服务获取方式
- **Spring AI 集成**: 使用 Spring AI 1.0.0 替代原生 HTTP 客户端

### 2. 依赖变更

```gradle
// 旧版本
api "com.theokanning.openai-gpt3-java:service:${openaiVersion}"

// 新版本
api "org.springframework.ai:spring-ai-openai:${springAiVersion}"
api "org.springframework.ai:spring-ai-core:${springAiVersion}"
```

## 迁移步骤

### 步骤 1: 更新依赖

在 `build.gradle` 中更新依赖：

```gradle
dependencies {
    // 移除旧依赖
    // api "com.theokanning.openai-gpt3-java:service:0.18.2"
    
    // 添加新依赖
    api "org.springframework.ai:spring-ai-openai:1.0.0-M6"
    api "org.springframework.ai:spring-ai-core:1.0.0-M6"
    implementation "org.springframework:spring-context:6.2.7"
}
```

### 步骤 2: 代码迁移

#### 2.1 OpenAIVectorGenerator 使用方式

**旧方式:**
```java
try (OpenAIVectorGenerator generator = new OpenAIVectorGenerator()) {
    float[] vector = generator.generateVector("文本");
}
```

**新方式 (推荐):**
```java
// 方式1: 使用工厂模式
OpenAIVectorGenerator generator = AiServiceFactory.getVectorGenerator();
float[] vector = generator.generateVector("文本");

// 方式2: 在 Spring 应用中使用依赖注入
@Autowired
private OpenAIVectorGenerator vectorGenerator;

public void someMethod() {
    float[] vector = vectorGenerator.generateVector("文本");
}
```

**兼容性说明:** 旧方式仍然支持，无需立即修改。

#### 2.2 AIDocumentationService 使用方式

**旧方式:**
```java
AIDocumentationService service = AIDocumentationService.getInstance();
String doc = service.generateDocumentationForLevel(context);
```

**新方式:**
```java
// 方式1: 使用工厂模式
AIDocumentationService service = AiServiceFactory.getDocumentationService();
String doc = service.generateDocumentationForLevel(context);

// 方式2: 在 Spring 应用中使用依赖注入
@Autowired
private AIDocumentationService documentationService;

public void someMethod() {
    String doc = documentationService.generateDocumentationForLevel(context);
}
```

#### 2.3 新增聊天服务

新版本提供了独立的聊天服务：

```java
SpringAiChatService chatService = AiServiceFactory.getChatService();
String response = chatService.chat("你好，请介绍一下 Spring Framework");
```

### 步骤 3: Spring 配置

如果您的应用使用 Spring，可以直接注入服务：

```java
@Service
public class MyService {
    
    @Autowired
    private OpenAIVectorGenerator vectorGenerator;
    
    @Autowired
    private SpringAiChatService chatService;
    
    @Autowired
    private AIDocumentationService documentationService;
    
    // 使用服务...
}
```

### 步骤 4: 非 Spring 应用配置

对于非 Spring 应用，使用工厂模式：

```java
public class MyApplication {
    public static void main(String[] args) {
        // 预热服务
        AiServiceFactory.warmUp();
        
        try {
            // 使用服务
            OpenAIVectorGenerator generator = AiServiceFactory.getVectorGenerator();
            // ... 业务逻辑
        } finally {
            // 关闭服务
            AiServiceFactory.shutdown();
        }
    }
}
```

## 常见问题

### Q1: 旧代码是否需要立即修改？

**A:** 不需要。新版本保持向后兼容，旧的使用方式仍然有效。但建议逐步迁移到新的工厂模式。

### Q2: 如何处理单例模式的依赖？

**A:** 将 `AIDocumentationService.getInstance()` 替换为 `AiServiceFactory.getDocumentationService()`。

### Q3: Spring AI 依赖下载失败怎么办？

**A:** 确保网络连接正常，或配置代理。也可以使用阿里云等镜像仓库。

### Q4: 如何验证迁移是否成功？

**A:** 运行集成测试：
```bash
./gradlew :code-graph-ai:test
```

## 性能优化建议

### 1. 服务预热

在应用启动时预热服务：

```java
@PostConstruct
public void init() {
    AiServiceFactory.warmUp();
}
```

### 2. 资源管理

在应用关闭时清理资源：

```java
@PreDestroy
public void cleanup() {
    AiServiceFactory.shutdown();
}
```

### 3. 异常处理

添加适当的异常处理：

```java
try {
    OpenAIVectorGenerator generator = AiServiceFactory.getVectorGenerator();
    float[] vector = generator.generateVector(text);
} catch (Exception e) {
    log.error("向量生成失败", e);
    // 处理异常
}
```

## 回滚方案

如果需要回滚到旧版本：

1. 恢复旧的依赖配置
2. 移除新增的 Spring AI 相关代码
3. 恢复单例模式的使用方式

## 技术支持

如果在迁移过程中遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 检查配置是否正确
3. 确认网络连接和 API 密钥
4. 参考示例代码和测试用例

## 总结

新版本的主要优势：

- **更好的架构**: Spring Bean 和依赖注入
- **更强的扩展性**: 基于 Spring AI 框架
- **更好的资源管理**: 自动管理生命周期
- **向后兼容**: 无需立即修改现有代码

建议采用渐进式迁移策略，先在新功能中使用新的 API，然后逐步迁移现有代码。
